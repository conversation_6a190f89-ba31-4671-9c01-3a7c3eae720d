import axios from "axios";
import { Telegraf } from "telegraf";
import OpenAI from "openai";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA, WMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";
import dayjs from "dayjs";

// Chart.js + plugins
import {
  Chart,
  TimeScale,
  LinearScale,
  BarController,
  BarElement,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  Title,
} from "chart.js";
// Note: Using linear scale for now due to date adapter issues in ES modules

// Dùng canvas để ghép nhiều biểu đồ
import { createCanvas, loadImage } from "canvas";

dotenv.config();

// === Config ===
const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = process.env.SYMBOL || "ETHUSDT";
const INTERVAL = process.env.INTERVAL || "1h";
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const chatId = process.env.TELEGRAM_GROUP_ID;
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// === Custom Candlestick Plugin ===
const CandlestickPlugin = {
  id: 'candlestick',
  afterDatasetsDraw(chart) {
    const { ctx, data, scales } = chart;
    const dataset = data.datasets.find(ds => ds.label && ds.label.includes(SYMBOL));
    if (!dataset || !dataset.data[0] || dataset.data[0].o === undefined) return;

    const xScale = scales.x;
    const yScale = scales.y;

    dataset.data.forEach((candle, index) => {
      if (!candle || candle.o === undefined) return;

      const x = xScale.getPixelForValue(candle.x);
      const yOpen = yScale.getPixelForValue(candle.o);
      const yHigh = yScale.getPixelForValue(candle.h);
      const yLow = yScale.getPixelForValue(candle.l);
      const yClose = yScale.getPixelForValue(candle.c);

      const isUp = candle.c >= candle.o;
      const candleWidth = Math.max(2, (xScale.width / dataset.data.length) * 0.6);

      // TradingView-style colors
      const upColor = '#26a69a';      // Green for bullish candles
      const downColor = '#ef5350';    // Red for bearish candles
      const upBorderColor = '#26a69a';
      const downBorderColor = '#ef5350';

      ctx.save();

      // Draw the wick (high-low line)
      ctx.strokeStyle = isUp ? upColor : downColor;
      ctx.lineWidth = 1;
      ctx.beginPath();
      ctx.moveTo(x, yHigh);
      ctx.lineTo(x, yLow);
      ctx.stroke();

      // Draw the body (open-close rectangle)
      const bodyTop = Math.min(yOpen, yClose);
      const bodyHeight = Math.max(1, Math.abs(yClose - yOpen)); // Ensure minimum height

      if (isUp) {
        // Bullish candle - filled with up color
        ctx.fillStyle = upColor;
        ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = upBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
      } else {
        // Bearish candle - filled with down color
        ctx.fillStyle = downColor;
        ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
        // Add subtle border
        ctx.strokeStyle = downBorderColor;
        ctx.lineWidth = 0.5;
        ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
      }

      ctx.restore();
    });
  }
};

// === Common chart register (gọi trong chartCallback của ChartJSNodeCanvas) ===
const registerAll = (Chart) => {
  Chart.register(
    TimeScale,
    LinearScale,
    BarController,
    BarElement,
    LineController,
    LineElement,
    PointElement,
    Tooltip,
    Legend,
    Title,
    CandlestickPlugin
  );
};

// === Fetch chart data from Binance ===
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 240 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
    closeTime: d[6],
  }));
}

// === Indicators (EMA, Sonic R PAC, RSI + EMA9 + WMA45) ===
function padToLen(arr, len) {
  return Array(len - arr.length).fill(null).concat(arr);
}

function calculateIndicators(candles) {
  const closes = candles.map((c) => c.close);
  const highs = candles.map((c) => c.high);
  const lows = candles.map((c) => c.low);
  const len = candles.length;

  // Sonic R PAC (EMA 34 của high/low/close)
  const pacLen = 34;
  const pacC = padToLen(EMA.calculate({ values: closes, period: pacLen }), len);
  const pacH = padToLen(EMA.calculate({ values: highs, period: pacLen }), len);
  const pacL = padToLen(EMA.calculate({ values: lows, period: pacLen }), len);

  // EMA
  const ema20 = padToLen(EMA.calculate({ values: closes, period: 20 }), len);
  const ema50 = padToLen(EMA.calculate({ values: closes, period: 50 }), len);
  const ema89 = padToLen(EMA.calculate({ values: closes, period: 89 }), len);
  const ema200 = padToLen(EMA.calculate({ values: closes, period: 200 }), len);
  const ema610 = padToLen(EMA.calculate({ values: closes, period: 610 }), len);

  // RSI + EMA9 + WMA45
  const rsi14 = RSI.calculate({ values: closes, period: 14 });
  const rsi = padToLen(rsi14, len);
  const rsiEma9 = padToLen(EMA.calculate({ values: rsi14, period: 9 }), len);
  const rsiWma45 = padToLen(WMA.calculate({ values: rsi14, period: 45 }), len);

  return { closes, pacC, pacH, pacL, ema20, ema50, ema89, ema200, ema610, rsi, rsiEma9, rsiWma45 };
}

// === Helpers ===
function inferTimeUnit(interval) {
  const s = interval.toLowerCase();
  if (s.endsWith("m")) return "minute";
  if (s.endsWith("h")) return "hour";
  if (s.endsWith("d")) return "day";
  return "hour";
}

// === Render single charts ===
async function renderPricePanel(candles, ind, width = 1200, height = 560) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const candleData = candles.map((c, i) => ({ x: i, o: c.open, h: c.high, l: c.low, c: c.close, time: c.time }));

  const config = {
    type: "line", // Use line as base type, candlesticks will be drawn by plugin
    data: {
      datasets: [
        {
          type: "line",
          label: `${SYMBOL} ${INTERVAL}`,
          data: candleData,
          showLine: false,
          pointRadius: 0,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        // Sonic R PAC
        {
          type: "line",
          label: "PAC High (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacH[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#ff9800',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "PAC Low (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacL[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#ff5722',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "PAC Close (EMA34)",
          data: candles.map((_, i) => ({ x: i, y: ind.pacC[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#9c27b0',
          backgroundColor: 'transparent'
        },
        // EMA overlays
        {
          type: "line",
          label: "EMA20",
          data: candles.map((_, i) => ({ x: i, y: ind.ema20[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#2196f3',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA50",
          data: candles.map((_, i) => ({ x: i, y: ind.ema50[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#4caf50',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA89",
          data: candles.map((_, i) => ({ x: i, y: ind.ema89[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#ffeb3b',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA200",
          data: candles.map((_, i) => ({ x: i, y: ind.ema200[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 2,
          borderColor: '#f44336',
          backgroundColor: 'transparent'
        },
        {
          type: "line",
          label: "EMA610",
          data: candles.map((_, i) => ({ x: i, y: ind.ema610[i] })),
          spanGaps: true,
          pointRadius: 0,
          borderWidth: 1,
          borderColor: '#795548',
          backgroundColor: 'transparent'
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        title: {
          display: true,
          text: `${SYMBOL} ${INTERVAL.toUpperCase()} Binance`,
          color: "#FFFFFF",
          font: { size: 18, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: { color: "#DDD" },
        },
        tooltip: {
          enabled: true,
          callbacks: {
            title: function(context) {
              const index = context[0].dataIndex;
              return dayjs(candles[index].time).format('YYYY-MM-DD HH:mm');
            },
            label: function(context) {
              const index = context.dataIndex;
              const candle = candleData[index];
              if (candle && candle.o !== undefined) {
                return [
                  `Open: ${candle.o.toFixed(4)}`,
                  `High: ${candle.h.toFixed(4)}`,
                  `Low: ${candle.l.toFixed(4)}`,
                  `Close: ${candle.c.toFixed(4)}`
                ];
              }
              return context.dataset.label + ': ' + context.parsed.y;
            }
          }
        },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              // Show time labels for every few ticks
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          position: "left",
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function renderRsiPanel(candles, ind, width = 1200, height = 180) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const config = {
    type: "line",
    data: {
      datasets: [
        {
          label: "RSI Upper 80",
          data: candles.map((_, i) => ({ x: i, y: 80 })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#666',
          backgroundColor: 'transparent'
        },
        {
          label: "RSI Lower 20",
          data: candles.map((_, i) => ({ x: i, y: 20 })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#666',
          backgroundColor: 'transparent'
        },
        {
          label: "RSI(14)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsi[i] })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: '#2196f3',
          backgroundColor: 'transparent'
        },
        {
          label: "EMA9(RSI)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsiEma9[i] })),
          borderWidth: 1,
          pointRadius: 0,
          borderColor: '#ff9800',
          backgroundColor: 'transparent'
        },
        {
          label: "WMA45(RSI)",
          data: candles.map((_, i) => ({ x: i, y: ind.rsiWma45[i] })),
          borderWidth: 2,
          pointRadius: 0,
          borderColor: '#9c27b0',
          backgroundColor: 'transparent'
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: true, labels: { color: "#DDD" } },
        title: { display: true, text: "RSI Panel", color: "#FFF" },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          min: 0,
          max: 100,
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function renderVolumePanel(candles, width = 1200, height = 160) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const volumeData = candles.map((c, i) => {
    const bgColor = i === 0 ? "rgba(38,166,154,0.6)" :
      (c.close >= candles[i - 1].close ? "rgba(38,166,154,0.6)" : "rgba(239,83,80,0.6)");
    return { x: i, y: c.volume, backgroundColor: bgColor };
  });

  const config = {
    type: "bar",
    data: {
      datasets: [
        {
          label: "Volume",
          data: volumeData,
          borderWidth: 0,
          backgroundColor: volumeData.map(d => d.backgroundColor)
        },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: false },
        title: { display: true, text: "Volume", color: "#FFF" },
      },
      scales: {
        x: {
          type: "linear",
          ticks: {
            color: "#AAA",
            maxTicksLimit: 12,
            callback: function(value, index) {
              if (index % Math.ceil(candles.length / 8) === 0 && candles[value]) {
                return dayjs(candles[value].time).format('MM/DD HH:mm');
              }
              return '';
            }
          },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

// === Compose 3 panels thành 1 ảnh ===
async function composePanels(priceBuf, rsiBuf, volBuf, outPath = "chart.png") {
  const priceImg = await loadImage(priceBuf);
  const rsiImg = await loadImage(rsiBuf);
  const volImg = await loadImage(volBuf);

  const gap = 6;
  const width = Math.max(priceImg.width, rsiImg.width, volImg.width);
  const height = priceImg.height + rsiImg.height + volImg.height + gap * 2;

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");

  // background
  ctx.fillStyle = "#0f1012";
  ctx.fillRect(0, 0, width, height);

  let y = 0;
  ctx.drawImage(priceImg, 0, y); y += priceImg.height + gap;
  ctx.drawImage(rsiImg, 0, y);   y += rsiImg.height + gap;
  ctx.drawImage(volImg, 0, y);

  const buffer = canvas.toBuffer("image/png");
  fs.writeFileSync(outPath, buffer);
  return outPath;
}

async function analyzeWithGPT(candles, ind) {
  const last = candles.at(-1);

  const text = `
Bạn là chuyên gia phân tích kỹ thuật. Phân tích ${SYMBOL} khung ${INTERVAL} với dữ liệu:
- Giá hiện tại: ${last.close}
- EMA20/50/89: ${[ind.ema20.at(-1), ind.ema50.at(-1), ind.ema89.at(-1)].map(v=>v?.toFixed(2)).join("/")}
- RSI(14): ${ind.rsi.at(-1)?.toFixed(1)}
- Sonic R PAC: ${ind.pacC.at(-1)?.toFixed(2)}
- Volume: ${last.volume}

BẮT BUỘC trả lời theo format HTML chính xác sau:

<b>📈 XU HƯỚNG:</b> [Tăng/Giảm/Sideway] - [Lý do ngắn gọn]

<b>🎯 KỊCH BẢN TRADE:</b>
• <b>Loại:</b> [LONG 🟢 / SHORT 🔴 / NO TRADE ⚪]
• <b>Setup:</b> [Điều kiện entry cụ thể]
• <b>Xác nhận:</b> [Tín hiệu cần chờ]
• <b>Entry/SL/TP:</b> Entry [giá], SL [giá], TP [giá] (RR 1:[tỷ lệ])
• <b>Invalidation:</b> [Điều kiện hủy kèo]

<b>💡 Độ tin cậy:</b> [X]%

QUAN TRỌNG:
- Chỉ sử dụng thẻ HTML <b> và </b>
- Không dùng **, ##, hay markdown khác
- Giá phải cụ thể, không mơ hồ
- Giới hạn 600 ký tự`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [
      { role: "system", content: "Bạn là chuyên gia trading. BẮT BUỘC trả lời theo CHÍNH XÁC format HTML được yêu cầu. Chỉ sử dụng thẻ <b></b>, không dùng ** hay markdown. Ngắn gọn, chuyên nghiệp." },
      { role: "user", content: text },
    ],
  });

  return res.choices[0].message.content;
}





// === Main Task ===
async function runBot() {
  const candles = await fetchData();
  const ind = calculateIndicators(candles);

  // render 3 panel
  const [priceBuf, rsiBuf, volBuf] = await Promise.all([
    renderPricePanel(candles, ind),
    renderRsiPanel(candles, ind),
    renderVolumePanel(candles),
  ]);
  const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

  // analysis
  const analysis = await analyzeWithGPT(candles, ind);

  // Send 1 message: photo + caption with HTML formatting
  await bot.telegram.sendPhoto(
    chatId,
    { source: chartPath },
    {
      caption: `📊 <b>${SYMBOL} (${INTERVAL})</b>\n\n${analysis}`,
      parse_mode: 'HTML'
    }
  );
}

// === Run every hour ===
setInterval(runBot, 60 * 60 * 1000);
runBot();
