import axios from "axios";
import { Telegraf } from "telegraf";
import OpenAI from "openai";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA, WMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";
import dayjs from "dayjs";

// Chart.js + plugins
import {
  TimeScale,
  LinearScale,
  BarController,
  BarElement,
  LineController,
  LineElement,
  PointElement,
  Tooltip,
  Legend,
  Title,
} from "chart.js";
import { CandlestickController, CandlestickElement } from "chartjs-chart-financial";
import "chartjs-adapter-date-fns";

// Dùng canvas để ghép nhiều biểu đồ
import { createCanvas, loadImage } from "canvas";

dotenv.config();

// === Config ===
const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = process.env.SYMBOL || "ETHUSDT";
const INTERVAL = process.env.INTERVAL || "1h";
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const chatId = process.env.TELEGRAM_GROUP_ID;
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// === Common chart register (gọi trong chartCallback của ChartJSNodeCanvas) ===
const registerAll = (Chart) => {
  Chart.register(
    TimeScale,
    LinearScale,
    BarController,
    BarElement,
    LineController,
    LineElement,
    PointElement,
    Tooltip,
    Legend,
    Title,
    CandlestickController,
    CandlestickElement
  );
};

// === Fetch chart data from Binance ===
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 240 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
    closeTime: d[6],
  }));
}

// === Indicators (EMA, Sonic R PAC, RSI + EMA9 + WMA45) ===
function padToLen(arr, len) {
  return Array(len - arr.length).fill(null).concat(arr);
}

function calculateIndicators(candles) {
  const closes = candles.map((c) => c.close);
  const highs = candles.map((c) => c.high);
  const lows = candles.map((c) => c.low);
  const len = candles.length;

  // Sonic R PAC (EMA 34 của high/low/close)
  const pacLen = 34;
  const pacC = padToLen(EMA.calculate({ values: closes, period: pacLen }), len);
  const pacH = padToLen(EMA.calculate({ values: highs, period: pacLen }), len);
  const pacL = padToLen(EMA.calculate({ values: lows, period: pacLen }), len);

  // EMA
  const ema20 = padToLen(EMA.calculate({ values: closes, period: 20 }), len);
  const ema50 = padToLen(EMA.calculate({ values: closes, period: 50 }), len);
  const ema89 = padToLen(EMA.calculate({ values: closes, period: 89 }), len);
  const ema200 = padToLen(EMA.calculate({ values: closes, period: 200 }), len);
  const ema610 = padToLen(EMA.calculate({ values: closes, period: 610 }), len);

  // RSI + EMA9 + WMA45
  const rsi14 = RSI.calculate({ values: closes, period: 14 });
  const rsi = padToLen(rsi14, len);
  const rsiEma9 = padToLen(EMA.calculate({ values: rsi14, period: 9 }), len);
  const rsiWma45 = padToLen(WMA.calculate({ values: rsi14, period: 45 }), len);

  return { closes, pacC, pacH, pacL, ema20, ema50, ema89, ema200, ema610, rsi, rsiEma9, rsiWma45 };
}

// === Helpers ===
function inferTimeUnit(interval) {
  const s = interval.toLowerCase();
  if (s.endsWith("m")) return "minute";
  if (s.endsWith("h")) return "hour";
  if (s.endsWith("d")) return "day";
  return "hour";
}

// === Render single charts ===
async function renderPricePanel(candles, ind, width = 1200, height = 560) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const candleData = candles.map((c) => ({ x: c.time, o: c.open, h: c.high, l: c.low, c: c.close }));

  const config = {
    type: "candlestick",
    data: {
      datasets: [
        {
          type: "candlestick",
          label: `${SYMBOL} ${INTERVAL}`,
          data: candleData,
        },
        // Sonic R PAC
        { type: "line", label: "PAC High (EMA34)", data: ind.pacH, spanGaps: true, pointRadius: 0, borderWidth: 1 },
        { type: "line", label: "PAC Low (EMA34)", data: ind.pacL, spanGaps: true, pointRadius: 0, borderWidth: 1 },
        { type: "line", label: "PAC Close (EMA34)", data: ind.pacC, spanGaps: true, pointRadius: 0, borderWidth: 2 },
        // EMA overlays
        { type: "line", label: "EMA20", data: ind.ema20, spanGaps: true, pointRadius: 0, borderWidth: 2 },
        { type: "line", label: "EMA50", data: ind.ema50, spanGaps: true, pointRadius: 0, borderWidth: 2 },
        { type: "line", label: "EMA89", data: ind.ema89, spanGaps: true, pointRadius: 0, borderWidth: 2 },
        { type: "line", label: "EMA200", data: ind.ema200, spanGaps: true, pointRadius: 0, borderWidth: 2 },
        { type: "line", label: "EMA610", data: ind.ema610, spanGaps: true, pointRadius: 0, borderWidth: 1 },
      ],
      labels: candles.map((c) => c.time),
    },
    options: {
      responsive: false,
      plugins: {
        title: {
          display: true,
          text: `${SYMBOL} ${INTERVAL.toUpperCase()} Binance`,
          color: "#FFFFFF",
          font: { size: 18, weight: "bold" },
        },
        legend: {
          display: true,
          position: "top",
          labels: { color: "#DDD" },
        },
        tooltip: { enabled: true },
      },
      scales: {
        x: {
          type: "time",
          time: { unit: inferTimeUnit(INTERVAL) },
          ticks: { color: "#AAA", maxTicksLimit: 12 },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
        y: {
          position: "left",
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.08)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function renderRsiPanel(candles, ind, width = 1200, height = 180) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const labels = candles.map((c) => c.time);
  const upper = Array(labels.length).fill(80);
  const lower = Array(labels.length).fill(20);

  const config = {
    type: "line",
    data: {
      labels,
      datasets: [
        { label: "RSI Upper 80", data: upper, borderWidth: 1, pointRadius: 0 },
        { label: "RSI Lower 20", data: lower, borderWidth: 1, pointRadius: 0 },
        { label: "RSI(14)", data: ind.rsi, borderWidth: 2, pointRadius: 0 },
        { label: "EMA9(RSI)", data: ind.rsiEma9, borderWidth: 1, pointRadius: 0 },
        { label: "WMA45(RSI)", data: ind.rsiWma45, borderWidth: 2, pointRadius: 0 },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: true, labels: { color: "#DDD" } },
        title: { display: true, text: "RSI Panel", color: "#FFF" },
      },
      scales: {
        x: {
          type: "time",
          time: { unit: inferTimeUnit(INTERVAL) },
          ticks: { color: "#AAA", maxTicksLimit: 12 },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          min: 0,
          max: 100,
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

async function renderVolumePanel(candles, width = 1200, height = 160) {
  const canvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: "#141416",
    chartCallback: registerAll,
  });

  const labels = candles.map((c) => c.time);
  const volumes = candles.map((c) => c.volume);

  const bgColors = candles.map((c, i) => {
    if (i === 0) return "rgba(38,166,154,0.6)";
    return c.close >= candles[i - 1].close ? "rgba(38,166,154,0.6)" : "rgba(239,83,80,0.6)";
  });

  const config = {
    type: "bar",
    data: {
      labels,
      datasets: [
        { label: "Volume", data: volumes, backgroundColor: bgColors, borderWidth: 0 },
      ],
    },
    options: {
      responsive: false,
      plugins: {
        legend: { display: false },
        title: { display: true, text: "Volume", color: "#FFF" },
      },
      scales: {
        x: {
          type: "time",
          time: { unit: inferTimeUnit(INTERVAL) },
          ticks: { color: "#AAA", maxTicksLimit: 12 },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
        y: {
          ticks: { color: "#AAA" },
          grid: { color: "rgba(255,255,255,0.05)" },
        },
      },
    },
  };

  return canvas.renderToBuffer(config, "image/png");
}

// === Compose 3 panels thành 1 ảnh ===
async function composePanels(priceBuf, rsiBuf, volBuf, outPath = "chart.png") {
  const priceImg = await loadImage(priceBuf);
  const rsiImg = await loadImage(rsiBuf);
  const volImg = await loadImage(volBuf);

  const gap = 6;
  const width = Math.max(priceImg.width, rsiImg.width, volImg.width);
  const height = priceImg.height + rsiImg.height + volImg.height + gap * 2;

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext("2d");

  // background
  ctx.fillStyle = "#0f1012";
  ctx.fillRect(0, 0, width, height);

  let y = 0;
  ctx.drawImage(priceImg, 0, y); y += priceImg.height + gap;
  ctx.drawImage(rsiImg, 0, y);   y += rsiImg.height + gap;
  ctx.drawImage(volImg, 0, y);

  const buffer = canvas.toBuffer("image/png");
  fs.writeFileSync(outPath, buffer);
  return outPath;
}

// === Analyze with GPT (ngắn gọn) ===
async function analyzeWithGPT(candles, ind) {
  const last = candles.at(-1);
  const text = `
Bạn là bot trade swing. ${SYMBOL} khung ${INTERVAL}.
Close: ${last.close}
EMA20/50/89: ${[ind.ema20.at(-1), ind.ema50.at(-1), ind.ema89.at(-1)].map(v=>v?.toFixed(2)).join("/")}
RSI(14)/EMA9/WMA45: ${[ind.rsi.at(-1), ind.rsiEma9.at(-1), ind.rsiWma45.at(-1)].map(v=>v?.toFixed?.(1)).join("/")}
Sonic R PAC: close@${ind.pacC.at(-1)?.toFixed(2)}

Cho 2 kịch bản (long/short) + điều kiện xác nhận, entry/SL/TP, invalidation. Tối đa 900 ký tự.`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [{ role: "user", content: text }],
  });
  return res.choices[0].message.content;
}

// === Main Task ===
async function runBot() {
  const candles = await fetchData();
  const ind = calculateIndicators(candles);

  // render 3 panel
  const [priceBuf, rsiBuf, volBuf] = await Promise.all([
    renderPricePanel(candles, ind),
    renderRsiPanel(candles, ind),
    renderVolumePanel(candles),
  ]);
  const chartPath = await composePanels(priceBuf, rsiBuf, volBuf, "chart.png");

  // analysis
  const analysis = await analyzeWithGPT(candles, ind);

  // Send 1 message: photo + caption
  await bot.telegram.sendPhoto(
    chatId,
    { source: chartPath },
    { caption: `📊 ${SYMBOL} (${INTERVAL})\n\n${analysis}` }
  );
}

// === Run every hour ===
setInterval(runBot, 60 * 60 * 1000);
runBot();
